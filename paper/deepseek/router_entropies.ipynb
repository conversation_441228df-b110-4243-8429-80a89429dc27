import torch
import torch.nn as nn

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import sys
import glob

import pandas as pd

save_folder = "/home/<USER>/moeut_training_code/paper/deepseek/router_saturation/smoe_shared"
router_info_list_path = glob.glob(os.path.join(save_folder, "*.npy"))
router_info_final_path = os.path.join(save_folder, "model-100000.npy")

# load all checkpoint information in the router_info_list_path
router_info_dict = {}
for path in router_info_list_path:
    router_info = np.load(path)
    router_info_dict[path] = router_info

# sort the router_info_dict by the number in key name
router_info_dict = sorted(router_info_dict.items(), key=lambda x: int(x[0].split("-")[-1].split(".")[0]))
router_info_dict = dict(router_info_dict)

def compute_entropy(router_info):
    n_layer = router_info.shape[0]
    router_infos = {}
    for layer in range(n_layer):
        router_info_layer = router_info[layer]  # num_tokens x num_experts (8000, 66)
        # print(router_info_layer.shape)
        
        total_entropy = 0
        for i in range(router_info_layer.shape[0]):  # num_tokens
            if np.sum(router_info_layer[i]) == 0 or np.isnan(np.sum(router_info_layer[i])):
                entropy = 0
            else:
                entropy = -np.sum(router_info_layer[i] * np.log(router_info_layer[i] + 1e-10))  # 1
                total_entropy += entropy
        total_entropy /= router_info_layer.shape[0]
        router_infos[layer] = total_entropy
    return router_infos

model_router_saturation_info = {}

for model_name, model_info in router_info_dict.items():
    count_match = compute_entropy(model_info)
    model_router_saturation_info[model_name] = count_match

model_router_saturation_info

# convert the model_router_saturation_info to pandas dataframe with column name is the number in the key name
model_router_saturation_info_df = pd.DataFrame(model_router_saturation_info)

# make the column name is the number in the key name
model_router_saturation_info_df.columns = [col.split("-")[-1].split(".")[0] for col in model_router_saturation_info_df.columns]
model_router_saturation_info_df.to_csv("model_router_saturation_info-smoe.csv", index=False)
model_router_saturation_info_df

# create a figure to visualize the model_router_saturation_info_df
# the x axis is the training step, which is the column name of the model_router_saturation_info_df, the y axis is the layer_id, which is the row name of the model_router_saturation_info_df

fig = plt.figure(figsize=(16, 10))
ax = fig.add_subplot(1, 1, 1)
ax.plot(model_router_saturation_info_df.columns, model_router_saturation_info_df.values.transpose(), marker="o")

# add the legend
ax.legend(model_router_saturation_info_df.index, loc="upper left")

ax.set_xlabel("Training Step")
ax.set_ylabel("Router Saturation")
ax.set_title("Router Saturation of S-MoAE")
plt.grid(True, alpha=0.2, linestyle="--")
plt.show()

# create a figure to visualize the model_router_saturation_info_df
# the x axis is the training step, which is the column name of the model_router_saturation_info_df, the y axis is the layer_id, which is the row name of the model_router_saturation_info_df

fig = plt.figure(figsize=(16, 10))
ax = fig.add_subplot(1, 1, 1)
ax.plot(model_router_saturation_info_df.columns, model_router_saturation_info_df.values.transpose(), marker="o")

# add the legend
ax.legend(model_router_saturation_info_df.index, loc="upper left")

ax.set_xlabel("Training Step")
ax.set_ylabel("Router Saturation")
ax.set_title("Router Saturation of S-MoAE")
plt.grid(True, alpha=0.2, linestyle="--")
plt.show()

def match_stats(router_choose, router_final_choose):
    n_layer = router_choose.shape[0]
    count_match = {}
    for layer in range(n_layer):
        count_match[layer] = 0
        router_choose_layer = router_choose[layer]
        router_final_choose_layer = router_final_choose[layer]
        
        # compare the choose of router_10k and router_final based only on the 1 value
        for i in range(router_choose_layer.shape[0]):
            for j in range(router_choose_layer[i].shape[0]):
                if router_choose_layer[i][j] == 1 and router_final_choose_layer[i][j] == 1:
                    count_match[layer] += 1
    return count_match

model_router_saturation_info = {}

for model_name, model_info in router_info_dict.items():
    router_choose = (model_info > 0).astype(int)
    count_match = match_stats(router_choose, router_final_choose)
    count_match_ratio = {layer: count_match[layer] / router_final_choose[layer].sum() * 100 for layer in count_match}
    model_router_saturation_info[model_name] = count_match_ratio

# convert the model_router_saturation_info to pandas dataframe with column name is the number in the key name
model_router_saturation_info_df = pd.DataFrame(model_router_saturation_info)

# make the column name is the number in the key name
model_router_saturation_info_df.columns = [col.split("-")[-1].split(".")[0] for col in model_router_saturation_info_df.columns]
model_router_saturation_info_df.to_csv("model_router_saturation_info-smoe.csv", index=False)
model_router_saturation_info_df

# create a figure to visualize the model_router_saturation_info_df
# the x axis is the training step, which is the column name of the model_router_saturation_info_df, the y axis is the layer_id, which is the row name of the model_router_saturation_info_df

fig = plt.figure(figsize=(16, 10))
ax = fig.add_subplot(1, 1, 1)
ax.plot(model_router_saturation_info_df.columns[:-1], model_router_saturation_info_df.values.transpose()[:-1], marker="o")

# add the legend
ax.legend(model_router_saturation_info_df.index)

ax.set_xlabel("Training Step")
ax.set_ylabel("Router Saturation")
ax.set_title("Router Saturation of S-MoAE")
plt.grid(True)
plt.show()

# convert the model_router_saturation_info to pandas dataframe with column name is the number in the key name
model_router_saturation_info_df = pd.DataFrame(model_router_saturation_info)

# make the column name is the number in the key name
model_router_saturation_info_df.columns = [col.split("-")[-1].split(".")[0] for col in model_router_saturation_info_df.columns]
model_router_saturation_info_df.to_csv("model_router_saturation_info-smoe.csv", index=False)
model_router_saturation_info_df

# create a figure to visualize the model_router_saturation_info_df
# the x axis is the training step, which is the column name of the model_router_saturation_info_df, the y axis is the layer_id, which is the row name of the model_router_saturation_info_df

fig = plt.figure(figsize=(16, 10))
ax = fig.add_subplot(1, 1, 1)
ax.plot(model_router_saturation_info_df.columns[:-1], model_router_saturation_info_df.values.transpose()[:-1], marker="o")

# add the legend
ax.legend(model_router_saturation_info_df.index)

ax.set_xlabel("Training Step")
ax.set_ylabel("Router Saturation")
ax.set_title("Router Saturation of S-MoAE")
plt.grid(True)
plt.show()

# convert the model_router_saturation_info to pandas dataframe with column name is the number in the key name
model_router_saturation_info_df = pd.DataFrame(model_router_saturation_info)

# make the column name is the number in the key name
model_router_saturation_info_df.columns = [col.split("-")[-1].split(".")[0] for col in model_router_saturation_info_df.columns]
model_router_saturation_info_df.to_csv("model_router_saturation_info-smoe.csv", index=False)
model_router_saturation_info_df

# create a figure to visualize the model_router_saturation_info_df
# the x axis is the training step, which is the column name of the model_router_saturation_info_df, the y axis is the layer_id, which is the row name of the model_router_saturation_info_df

fig = plt.figure(figsize=(16, 10))
ax = fig.add_subplot(1, 1, 1)
ax.plot(model_router_saturation_info_df.columns[:-1], model_router_saturation_info_df.values.transpose()[:-1], marker="o")

# add the legend
ax.legend(model_router_saturation_info_df.index)

ax.set_xlabel("Training Step")
ax.set_ylabel("Router Saturation")
ax.set_title("Router Saturation of S-MoAE")
plt.grid(True)
plt.show()


