import json
import numpy as np
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

event_smoe_679m = EventAccumulator('/home/<USER>/tensorboard/679m/smoe/tensorboard')
event_smoe_679m.Reload()  # Load the events
event_dense_679m = EventAccumulator('/home/<USER>/tensorboard/679m/dense/tensorboard')
event_dense_679m.Reload()  # Load the events

event_smoe_158m = EventAccumulator('/home/<USER>/tensorboard/158m/smoe/tensorboard')
event_smoe_158m.Reload()  # Load the events
event_dense_158m = EventAccumulator('/home/<USER>/tensorboard/158m/dense/tensorboard')
event_dense_158m.Reload()  # Load the events